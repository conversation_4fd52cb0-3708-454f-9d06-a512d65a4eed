.change-time-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .change-time-card {
    max-width: 500px;
    width: 100%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 16px;

    mat-card-header {
      text-align: center;
      padding-bottom: 20px;

      mat-card-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      mat-card-subtitle {
        font-size: 14px;
        color: #666;
      }
    }

    mat-card-content {
      padding: 20px;

      .form-container {
        .date-time-inputs {
          display: flex;
          gap: 16px;
          margin-bottom: 24px;

          .date-field,
          .time-field {
            flex: 1;
          }

          @media (max-width: 480px) {
            flex-direction: column;
            gap: 12px;
          }
        }

        .current-target-info {
          background: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
          border-left: 4px solid #667eea;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }

          .target-display {
            margin: 0;
            font-size: 14px;
            color: #555;
            font-weight: 500;
          }
        }
      }
    }

    mat-card-actions {
      padding: 20px;
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;

      .save-btn {
        min-width: 120px;
      }

      .reset-btn,
      .cancel-btn {
        min-width: 100px;
      }

      @media (max-width: 480px) {
        flex-direction: column;
        
        button {
          width: 100%;
        }
      }
    }
  }
}

// Custom snackbar styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }

  .info-snackbar {
    background-color: #2196f3 !important;
    color: white !important;
  }
}
