import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { AllCustomerCards, AllCardsOfUser, AllAchOfUser } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { SignedDocumentsInfo } from '../../models';
import { Account } from 'src/app/auth/models/user.model';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PaymentMethodsComponent } from '../../../../shared/components/payment-methods/payment-methods.component';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatIconModule, MatButtonModule, SharedModule, MatSidenavModule],
  PIPES: [LocalDatePipe],
  COMPONENTS: [PaymentMethodsComponent]
};

@Component({
  selector: 'app-plan-recurring-payment',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-recurring-payment.component.html',
  styleUrl: './plan-recurring-payment.component.scss'
})
export class PlanRecurringPaymentComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedPlanDetail!: SignedDocumentsInfo | null;
  @Input() currentUser$!: Account | null;
  @Input() defaultCard!: AllCardsOfUser | null;
  @Input() defaultAch!: AllAchOfUser | null;
  @Input() totalProcessingFee!: number;
  @Input() firstPaymentProcessingFee!: number;

  allCustomerCards!: AllCustomerCards;
  isBillingSideNavOpen = false;

  @Output() getCustomerCard = new EventEmitter<void>();

  constructor(
    private readonly paymentService: PaymentService,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.currentUser$) {
      this.currentUser = this.currentUser$;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentUser$']?.currentValue) {
      this.currentUser = changes['currentUser$'].currentValue;
    }
  }

  getAllCustomerCards(): void {
    this.showPageLoader = false;

    if (!this.currentUser?.userId) {
      return;
    }

    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          if (res.result) {
            this.defaultCard= res.result.getAllCardsOfUser?.find(card => card.isDefault) || null;
            this.defaultAch = res.result.getAllAchDetailsOfUser?.find(ach => ach.isDefault) || null;

            if (this.defaultCard) {
              this.getProcessingFeeForCard();
            }
          }
          this.getCustomerCard.emit();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  get getDaysOfTheWeek(): string {
    return this.selectedPlanDetail ? this.schedulerService.getDaysOfWeek(this.selectedPlanDetail.daysOfSchedule) : '';
  }

  get calculateTotalAmount(): number {
    if (this.selectedPlanDetail) {
      return (
        this.selectedPlanDetail.firstPayment +
        this.selectedPlanDetail.serviceFees +
        (this.defaultCard ? this.firstPaymentProcessingFee : 0) +
        this.selectedPlanDetail.registrationFees -
        this.selectedPlanDetail.discountedAmount
      );
    }
    return 0;
  }

  getProcessingFeeForCard(): void {
    if (!this.selectedPlanDetail) {
      return;
    }
    if (this.selectedPlanDetail?.serviceFees + this.selectedPlanDetail?.registrationFees + this.selectedPlanDetail?.firstPayment - this.selectedPlanDetail?.discountedAmount === 0) {
      this.firstPaymentProcessingFee = 0;
      this.totalProcessingFee = 0;
      return;
    }
    this.totalProcessingFee = +((this.selectedPlanDetail?.planAmount ?? 0) * 0.035).toFixed(2);

    if (this.selectedPlanDetail?.serviceFees && this.selectedPlanDetail?.registrationFees) {
      this.firstPaymentProcessingFee = +(
        (this.selectedPlanDetail?.firstPayment + this.selectedPlanDetail?.serviceFees + this.selectedPlanDetail?.registrationFees) *
        0.035
      );
    }
  }
}
