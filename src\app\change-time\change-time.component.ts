import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { TargetDateService } from '../shared/services/target-date.service';
import { ROUTER_PATHS } from '../shared/constants';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatSnackBarModule
  ]
};

@Component({
  selector: 'app-change-time',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './change-time.component.html',
  styleUrl: './change-time.component.scss'
})
export class ChangeTimeComponent implements OnInit {
  selectedDate!: Date;
  selectedTime!: string;

  constructor(
    private readonly targetDateService: TargetDateService,
    private readonly router: Router,
    private readonly snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeDateTime();
  }

  private initializeDateTime(): void {
    const currentTargetDate = this.targetDateService.getTargetDate();
    this.selectedDate = new Date(currentTargetDate);
    
    // Format time as HH:MM for the time input
    const hours = currentTargetDate.getHours().toString().padStart(2, '0');
    const minutes = currentTargetDate.getMinutes().toString().padStart(2, '0');
    this.selectedTime = `${hours}:${minutes}`;
  }

  onSave(): void {
    if (!this.selectedDate || !this.selectedTime) {
      this.snackBar.open('Please select both date and time', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Combine date and time
    const [hours, minutes] = this.selectedTime.split(':').map(Number);
    const newTargetDate = new Date(this.selectedDate);
    newTargetDate.setHours(hours, minutes, 0, 0);

    // Update the target date
    this.targetDateService.setTargetDate(newTargetDate);

    this.snackBar.open('Target date updated successfully!', 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });

    // Navigate back to launch page after a short delay
    setTimeout(() => {
      this.router.navigate([ROUTER_PATHS.launchPage]);
    }, 1500);
  }

  onCancel(): void {
    this.router.navigate([ROUTER_PATHS.launchPage]);
  }

  onReset(): void {
    this.targetDateService.resetToDefault();
    this.initializeDateTime();
    
    this.snackBar.open('Target date reset to default', 'Close', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }
}
