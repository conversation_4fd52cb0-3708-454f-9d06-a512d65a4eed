<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ selectedRoom ? "Edit " : "Add " }} Room Schedule</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onAddRoomSchedule()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : roomScheduleForm"></ng-container>
  </div>
</div>

<ng-template #roomScheduleForm>
  <form [formGroup]="addRoomScheduleForm">
    @if (selectedRoom) {
    <div class="field-wrapper">
      <label class="required mb-0">Select Edit Option</label>
      <div>
        <div class="btn-typed-options-wrapper">
          @for (recurringScheduleOption of constants.editRecurringScheduleOptions; track $index) {
          <div
            [ngClass]="{
              'btn-typed-option': true,
              active: addRoomScheduleForm.controls.isAllInstances.value === recurringScheduleOption.value
            }"
            (click)="
              setFormControlValue('isAllInstances', recurringScheduleOption.value);
              onRecurringEditOptionChange(recurringScheduleOption.value)
            ">
            {{ recurringScheduleOption.label }}
          </div>
          }
        </div>
      </div>
    </div>
    }
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Select Location</label>
      <div class="w-100">
        <mat-form-field class="w-100 mat-select-custom">
          <mat-select
            formControlName="locationId"
            placeholder="Select Location"
            (selectionChange)="getRooms(); resetInstructors()">
            <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
              {{ location.schoolLocations.locationName }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.locationId"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="field-wrapper field-with-mat-inputs" *ngIf="addRoomScheduleForm.controls.locationId.value">
      <label class="required">Select Room</label>
      <div class="w-100">
        <mat-form-field class="w-100 mat-select-custom">
          <mat-select formControlName="roomId" placeholder="Select Room">
            <mat-option
              *ngFor="let room of rooms"
              [value]="room.roomDetail.id"
              (click)="getInstrumentNames(room.roomDetail.roomInstrumentList); resetInstruments()">
              {{ room.roomDetail.roomName }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.roomId"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- @if (addRoomScheduleForm.controls.roomId.value) {
      <div class="field-wrapper mb-4">
        <label class="mb-0">Instruments in there</label>
        <div class="field-content">
          @if (instruments && instruments.length) {
            @for (instrument of instruments; track $index) {
              <div class="primary-color">{{ instrument.quantity }} {{ instrument.instrumentName }}</div>
              <div *ngIf="$index < instruments.length - 1" class="dot"></div>
            }
          } @else {
            <div class="primary-color">No Instrument Available</div>
          }
        </div>
      </div>
    } -->

    <div class="field-wrapper field-with-mat-inputs" *ngIf="addRoomScheduleForm.controls.roomId.value">
      <label class="required">Select Instructor</label>
      <div class="w-100">
        @if (showInstructorLoader) {
        <ng-container [ngTemplateOutlet]="showSpinner"></ng-container>
        } @else {
        <mat-form-field class="w-100 mat-select-custom">
          <mat-select
            formControlName="instructorId"
            placeholder="Select Instructor"
            (ngModelChange)="getCommonInstruments(); resetInstruments()">
            @if (instructors && instructors.length) {
            <mat-option *ngFor="let instructor of instructors" [value]="instructor.instructorDetail.id">
              <div class="instructor-name">{{ instructor.instructorDetail.name }}</div>
            </mat-option>
            } @else {
            <mat-option>No Instructor Available</mat-option>
            }
          </mat-select>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.instructorId"></app-error-messages>
          </mat-error>
        </mat-form-field>
        }
      </div>
    </div>

    <div
      class="field-wrapper field-with-mat-inputs"
      *ngIf="addRoomScheduleForm.controls.locationId.value && addRoomScheduleForm.controls.roomId.value">
      <label class="required">Select Instrument</label>
      <div class="w-100">
        <app-multi-select-chips
          class="w-100 mat-select-custom"
          [filterDetail]="roomParams.instrumentId"
          (selectedFilterValues)="setInstrumentIds()"></app-multi-select-chips>
        <mat-error>
          <app-error-messages [control]="addRoomScheduleForm.controls.instumentIds"></app-error-messages>
        </mat-error>
      </div>
    </div>

    @if(!addRoomScheduleForm.controls.isAllInstances.value && selectedRoom) {
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Schedule Date</label>
      <div class="field-content">
        <mat-form-field class="mat-start-date w-100">
          <input
            matInput
            [matDatepicker]="startPicker"
            (click)="startPicker.open()"
            formControlName="scheduleStartDate"
            (dateChange)="onChangeScheduleDate()"
            placeholder="Select Schedule Date"
            [min]="maxDate" />
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.scheduleStartDate"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    } @else {
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Start Date - End Date</label>
      <div class="field-content">
        <mat-form-field class="mat-start-date w-100">
          <input
            matInput
            [matDatepicker]="startPicker"
            (click)="startPicker.open()"
            formControlName="scheduleStartDate"
            (dateChange)="addRoomScheduleForm.controls.scheduleEndDate.reset()"
            placeholder="Select Start Date"
            [min]="selectedRoom?.scheduleStartDate ?? maxDate" />
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.scheduleStartDate"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <div class="dash mb-4">-</div>
        <mat-form-field class="mat-start-date w-100">
          <input
            matInput
            [matDatepicker]="endPicker"
            (click)="endPicker.open()"
            formControlName="scheduleEndDate"
            placeholder="Select End Date"
            [min]="
              addRoomScheduleForm.controls.scheduleStartDate.value
                ? addRoomScheduleForm.controls.scheduleStartDate.value
                : maxDate
            " />
          <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.scheduleEndDate"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    }

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Start Time - End Time</label>
      <div class="field-content">
        <div class="w-100">
          <ngx-timepicker-field
            formControlName="start"
            [defaultTime]="selectedRoom ? getFormattedTime('start') : ''"
            [minutesGap]="constants.fiveMinutesGap"
            (timeChanged)="setTime('start')"></ngx-timepicker-field>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.start"></app-error-messages>
          </mat-error>
        </div>
        <div class="dash mb-4">-</div>
        <div class="w-100">
          <ngx-timepicker-field
            formControlName="end"
            [defaultTime]="selectedRoom ? getFormattedTime('end') : ''"
            [minutesGap]="constants.fiveMinutesGap"
            (timeChanged)="setTime('end')"></ngx-timepicker-field>
          <mat-error>
            <app-error-messages [control]="addRoomScheduleForm.controls.end"></app-error-messages>
          </mat-error>
        </div>
      </div>
    </div>

    @if (addRoomScheduleForm.controls.isAllInstances.value || !selectedRoom) {
    <div class="field-wrapper">
      <label class="required">Select Weekday(s)</label>
      <div class="w-100">
        <div class="single-btn-select-wrapper">
          @for (day of constants.daysOfTheWeek; track $index) {
          <div [ngClass]="{ active: isDaySelected(day.value) }" class="select-btn" (click)="setDaysOfWeek(day.value)">
            {{ day.label }}
          </div>
          }
        </div>
        <mat-error>
          <app-error-messages [control]="addRoomScheduleForm.controls.scheduleDays"></app-error-messages>
        </mat-error>
      </div>
    </div>
    }
  </form>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #showSpinner>
  <div class="instructor-loader-wrapper">
    <div class="loader"></div>
    <span class="instructor-loading">Please wait while the instructors are loading...</span>
  </div>
</ng-template>
