import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LocalStorageService, StorageItem } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class TargetDateService {
  private readonly defaultTargetDate = new Date('2026-01-16T15:00:00');
  private readonly targetDateSubject = new BehaviorSubject<Date>(this.getStoredTargetDate());
  
  readonly targetDate$: Observable<Date> = this.targetDateSubject.asObservable();

  constructor(private readonly localStorageService: LocalStorageService) {}

  private getStoredTargetDate(): Date {
    const storedDate = this.localStorageService.getItem(StorageItem.TargetDate);
    return storedDate ? new Date(storedDate) : this.defaultTargetDate;
  }

  getTargetDate(): Date {
    return this.targetDateSubject.value;
  }

  setTargetDate(date: Date): void {
    this.targetDateSubject.next(date);
    this.localStorageService.setItem(StorageItem.TargetDate, date.toISOString());
  }

  resetToDefault(): void {
    this.setTargetDate(this.defaultTargetDate);
  }
}
