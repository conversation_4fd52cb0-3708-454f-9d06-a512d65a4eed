<div class="change-time-container">
  <mat-card class="change-time-card">
    <mat-card-header>
      <mat-card-title>Change Launch Target Date & Time</mat-card-title>
      <mat-card-subtitle>Set the countdown target for the launch page</mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-content>
      <div class="form-container">
        <div class="date-time-inputs">
          <mat-form-field appearance="outline" class="date-field">
            <mat-label>Target Date</mat-label>
            <input 
              matInput 
              [matDatepicker]="picker" 
              [(ngModel)]="selectedDate"
              placeholder="Select date"
              readonly>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" class="time-field">
            <mat-label>Target Time</mat-label>
            <input 
              matInput 
              type="time" 
              [(ngModel)]="selectedTime"
              placeholder="Select time">
          </mat-form-field>
        </div>

        <div class="current-target-info">
          <h4>Current Target:</h4>
          <p class="target-display">
            {{ selectedDate | date:'fullDate' }} at {{ selectedTime }}
          </p>
        </div>
      </div>
    </mat-card-content>

    <mat-card-actions class="action-buttons">
      <button 
        mat-raised-button 
        color="primary" 
        (click)="onSave()"
        class="save-btn">
        Save Changes
      </button>
      
      <button 
        mat-stroked-button 
        (click)="onCancel()"
        class="cancel-btn">
        Cancel
      </button>
    </mat-card-actions>
  </mat-card>
</div>
